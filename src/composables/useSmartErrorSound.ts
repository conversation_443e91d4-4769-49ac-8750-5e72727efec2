import { onUnmounted } from 'vue';
import { useErrorSound, type ErrorType, type SoundType } from './useErrorSound';
import { createLocalStorage } from '/@/utils/cache';

/**
 * 智能错误语音播放 Composable
 * 提供防重复、防抖功能，避免快速连续推送导致的语音重叠
 */
export const useSmartErrorSound = () => {
  const { playErrorSound } = useErrorSound();

  // 错误播放控制状态
  let lastErrorPlayTime = 0;
  let lastErrorHash = '';
  let errorPlayDebounceTimer: NodeJS.Timeout | null = null;

  /**
   * 生成错误列表的哈希值，用于去重判断
   */
  const generateErrorHash = (actionLabels: { actionType: number }[]) => {
    if (!actionLabels?.length) return '';
    return actionLabels
      .map(label => label.actionType)
      .sort()
      .join(',');
  };

  /**
   * 清理防抖定时器
   */
  const clearDebounceTimer = () => {
    if (errorPlayDebounceTimer) {
      clearTimeout(errorPlayDebounceTimer);
      errorPlayDebounceTimer = null;
    }
  };

  /**
   * 智能播放错误提示音
   * @param actionLabels 错误标签列表
   * @param soundType 声音类型，可选，默认从本地存储获取
   * @param options 配置选项
   */
  const playErrorSoundSmart = (
    actionLabels: { actionType: number }[],
    soundType?: SoundType,
    options: {
      /** 防重复时间间隔（毫秒），默认2000ms */
      duplicateInterval?: number;
      /** 防抖延迟时间（毫秒），默认300ms */
      debounceDelay?: number;
      /** 是否启用声音，默认从本地存储获取 */
      soundEnabled?: boolean;
    } = {}
  ) => {
    const {
      duplicateInterval = 2000,
      debounceDelay = 300,
      soundEnabled
    } = options;

    // 检查是否启用声音
    const ls = createLocalStorage();
    const isSoundEnabled = soundEnabled !== undefined
      ? soundEnabled
      : ls.get('soundEnabled') !== false; // 默认启用

    if (!isSoundEnabled || !actionLabels?.length) {
      return;
    }

    // 获取声音类型
    const finalSoundType = soundType || (ls.get('soundType') || 'rational') as SoundType;

    const currentErrorHash = generateErrorHash(actionLabels);
    const now = Date.now();

    // 如果是相同的错误且时间间隔小于指定时间，则忽略
    if (currentErrorHash === lastErrorHash && (now - lastErrorPlayTime) < duplicateInterval) {
      return;
    }

    // 清除之前的防抖定时器
    clearDebounceTimer();

    // 使用防抖机制，延迟播放
    errorPlayDebounceTimer = setTimeout(() => {
      lastErrorPlayTime = now;
      lastErrorHash = currentErrorHash;

      playErrorSound(
        actionLabels as { actionType: ErrorType }[],
        finalSoundType
      );
    }, debounceDelay);
  };

  /**
   * 重置播放状态
   * 清除所有缓存的状态和定时器
   */
  const resetPlayState = () => {
    clearDebounceTimer();
    lastErrorPlayTime = 0;
    lastErrorHash = '';
  };

  // 组件卸载时自动清理
  onUnmounted(() => {
    clearDebounceTimer();
  });

  return {
    playErrorSoundSmart,
    resetPlayState,
    clearDebounceTimer
  };
};

/**
 * 用于操作日志组件的智能错误播放
 * 专门处理操作日志中的错误播放逻辑
 */
export const useOperationLogErrorSound = () => {
  const { playErrorSoundSmart } = useSmartErrorSound();

  /**
   * 处理操作日志的错误播放
   * @param operationLog 操作日志对象
   * @param soundType 声音类型，可选
   */
  const handleOperationLogError = (
    operationLog: {
      result: number;
      actionLabels?: { actionType: number }[]
    },
    soundType?: SoundType
  ) => {
    // 检查是否为错误结果（0=不合格，2=不达标）
    if ((operationLog.result === 0 || operationLog.result === 2) && operationLog.actionLabels?.length) {
      playErrorSoundSmart(operationLog.actionLabels, soundType);
    }
  };

  return {
    handleOperationLogError,
    playErrorSoundSmart
  };
};
