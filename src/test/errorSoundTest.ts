import { useSmartErrorSound } from '../composables/useSmartErrorSound';
import { useErrorSound, type ErrorType } from '/@/composables/useErrorSound';

// 测试数据：模拟间隔2秒同时推送3条多个错误的数据
export const createErrorSoundTestData = () => {
  // 第一批错误数据（时间戳：0ms）
  const errorBatch1 = [
    { actionType: 1 as ErrorType }, // 未双手作业
    { actionType: 2 as ErrorType }, // 未垂直作业面
    { actionType: 3 as ErrorType }, // 拧紧未贴合
  ];

  // 第二批错误数据（时间戳：500ms）
  const errorBatch2 = [
    { actionType: 4 as ErrorType }, // 拧紧枪未亮绿灯
    { actionType: 8 as ErrorType }, // 未戴手套
    { actionType: 21 as ErrorType }, // 堵盖选择错误
  ];

  // 第三批错误数据（时间戳：1000ms）- 这是最后一批，应该被完整播放
  const errorBatch3 = [
    { actionType: 22 as ErrorType }, // 堵盖不贴合
    { actionType: 31 as ErrorType }, // 安装不规范
    { actionType: 41 as ErrorType }, // 油管未闭合
    { actionType: 51 as ErrorType }, // 安装完成未回拔
  ];

  return {
    errorBatch1,
    errorBatch2,
    errorBatch3,
  };
};

// 测试函数：模拟新错误推送打断当前播放
export const testErrorSoundOverlap = async () => {
  const { playErrorSound } = useErrorSound();
   const { playErrorSoundSmart } = useSmartErrorSound();
  const { errorBatch1, errorBatch2, errorBatch3 } = createErrorSoundTestData();

  console.log('开始测试错误语音重叠问题...');

  // 模拟第一批错误（立即推送）
  console.log('推送第一批错误（3个错误）:', errorBatch1.map(e => e.actionType));
  playErrorSoundSmart(errorBatch1, 'rational');

  // 模拟第二批错误（500ms后推送，打断第一批）
  // setTimeout(() => {
    console.log('推送第二批错误（3个错误）- 打断第一批播放:', errorBatch2.map(e => e.actionType));
    playErrorSoundSmart(errorBatch2, 'rational');
  // }, 500);

  // 模拟第三批错误（1000ms后推送，打断第二批，这是最后一批）
  setTimeout(() => {
    console.log('推送第三批错误（4个错误）- 打断第二批播放，最后一批应该被完整播放:', errorBatch3.map(e => e.actionType));
    playErrorSoundSmart(errorBatch3, 'rational');
  }, 1000);

  console.log('测试说明：');
  console.log('- 第一批错误开始播放后，500ms和1000ms会有新的错误推送');
  console.log('- 任何新的错误推送都会立即停止当前播放');
  console.log('- 最终只会播放最后一批错误（第三批的4个错误）');
  console.log('- 每个错误都会依次播放完整，不会有重叠');
};

// 测试函数：模拟快速连续推送相同错误（模拟你遇到的情况）
export const testRapidDuplicateErrors = async () => {
  const { playErrorSound } = useErrorSound();

  console.log('开始测试快速连续推送相同错误...');

  // 模拟你截图中的情况：快速连续推送相同的错误
  const sameErrors = [
    { actionType: 1 as ErrorType }, // 未双手作业
    { actionType: 2 as ErrorType }, // 未垂直作业面
    { actionType: 8 as ErrorType }, // 未戴手套
  ];

  console.log('模拟快速连续推送相同错误（间隔100ms）:', sameErrors.map(e => e.actionType));

  // 第一次推送
  console.log('第1次推送 - 时间:', new Date().toLocaleTimeString());
  playErrorSound(sameErrors, 'rational');

  // 第二次推送（100ms后）
  setTimeout(() => {
    console.log('第2次推送 - 时间:', new Date().toLocaleTimeString());
    playErrorSound(sameErrors, 'rational');
  }, 100);

  // 第三次推送（200ms后）
  setTimeout(() => {
    console.log('第3次推送 - 时间:', new Date().toLocaleTimeString());
    playErrorSound(sameErrors, 'rational');
  }, 200);

  // 第四次推送（300ms后）
  setTimeout(() => {
    console.log('第4次推送 - 时间:', new Date().toLocaleTimeString());
    playErrorSound(sameErrors, 'rational');
  }, 300);

  console.log('测试说明：');
  console.log('- 模拟快速连续推送相同的错误（间隔100ms）');
  console.log('- 应该只播放最后一次推送的错误');
  console.log('- 前面的推送应该被去重或防抖机制过滤掉');
  console.log('- 不应该出现语音重叠');
};

// 测试函数：正常间隔的错误推送（对比测试）
export const testErrorSoundNormal = async () => {
  const { playErrorSound } = useErrorSound();
  const { errorBatch1, errorBatch2, errorBatch3 } = createErrorSoundTestData();

  console.log('开始测试正常间隔的错误语音播放...');

  // 第一批错误
  console.log('推送第一批错误（3个错误）:', errorBatch1.map(e => e.actionType));
  playErrorSound(errorBatch1, 'rational');

  // 第二批错误（8秒后推送，确保第一批播放完毕）
  setTimeout(() => {
    console.log('推送第二批错误（3个错误）:', errorBatch2.map(e => e.actionType));
    playErrorSound(errorBatch2, 'rational');
  }, 8000);

  // 第三批错误（16秒后推送，确保第二批播放完毕）
  setTimeout(() => {
    console.log('推送第三批错误（4个错误）:', errorBatch3.map(e => e.actionType));
    playErrorSound(errorBatch3, 'rational');
  }, 16000);

  console.log('测试说明：');
  console.log('- 每批错误间隔8秒推送，确保前一批完全播放完毕');
  console.log('- 每批错误都会完整播放');
  console.log('- 不会出现重叠或中断');
};

// 测试函数：模拟播放过程中被打断的场景
export const testErrorSoundInterruption = async () => {
  const { playErrorSound } = useErrorSound();
  const { errorBatch1, errorBatch3 } = createErrorSoundTestData();

  console.log('开始测试播放过程中被打断的场景...');

  // 开始播放一个较长的错误序列
  console.log('推送第一批错误（3个错误）- 开始播放:', errorBatch1.map(e => e.actionType));
  playErrorSound(errorBatch1, 'rational');

  // 在播放过程中（预计第一个或第二个错误播放时）推送新错误
  setTimeout(() => {
    console.log('播放过程中推送新错误（4个错误）- 应该立即停止当前播放:', errorBatch3.map(e => e.actionType));
    playErrorSound(errorBatch3, 'rational');
  }, 2000); // 2秒后打断，通常第一个错误还在播放或刚播放完

  console.log('测试说明：');
  console.log('- 第一批错误开始播放');
  console.log('- 2秒后（通常在播放第一个或第二个错误时）推送新错误');
  console.log('- 当前播放应该立即停止，开始播放新的错误序列');
  console.log('- 新的错误序列应该完整播放，不被打断');
};

// 单个错误测试
export const testSingleError = () => {
  const { playErrorSound } = useErrorSound();

  console.log('测试单个错误播放...');
  playErrorSound([{ actionType: 1 as ErrorType }], 'rational');
};

// 获取错误信息的辅助函数
export const getErrorDescription = (errorType: ErrorType) => {
  const { getErrorInfo } = useErrorSound();
  const errorInfo = getErrorInfo(errorType);
  return `${errorType}: ${errorInfo.description} - ${errorInfo.errorMessage}`;
};

// 打印所有测试数据的错误信息
export const printTestErrorInfo = () => {
  const { errorBatch1, errorBatch2, errorBatch3 } = createErrorSoundTestData();

  console.log('=== 测试数据错误信息 ===');

  console.log('第一批错误:');
  errorBatch1.forEach(error => {
    console.log(`  ${getErrorDescription(error.actionType)}`);
  });

  console.log('第二批错误:');
  errorBatch2.forEach(error => {
    console.log(`  ${getErrorDescription(error.actionType)}`);
  });

  console.log('第三批错误:');
  errorBatch3.forEach(error => {
    console.log(`  ${getErrorDescription(error.actionType)}`);
  });
};

// 模拟智能错误播放测试器
class SmartErrorSoundTester {
  constructor() {
    this.isPlaying = false;
    this.debounceTimer = null;
    this.lastErrorHash = '';
    this.lastPlayTime = 0;
    this.currentQueue = [];
    this.currentIndex = 0;
    this.logs = [];
  }

  log(message, type = 'info') {
    const time = new Date().toLocaleTimeString();
    const logEntry = `[${time}] ${message}`;
    this.logs.push({ time, message, type });
    console.log(`%c${logEntry}`, this.getLogStyle(type));
  }

  getLogStyle(type) {
    const styles = {
      info: 'color: #007bff;',
      warn: 'color: #ffc107; font-weight: bold;',
      error: 'color: #dc3545; font-weight: bold;',
      success: 'color: #28a745; font-weight: bold;'
    };
    return styles[type] || styles.info;
  }

  generateErrorHash(actionLabels) {
    if (!actionLabels?.length) return '';
    return actionLabels
      .map(label => label.actionType)
      .sort()
      .join(',');
  }

  clearDebounceTimer() {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
  }

  stopCurrentPlayback() {
    this.clearDebounceTimer();
    this.isPlaying = false;
    this.currentQueue = [];
    this.currentIndex = 0;
    this.log('播放已停止', 'warn');
  }

  async playErrorSoundSmart(actionLabels, options = {}) {
    const {
      duplicateInterval = 2000,
      debounceDelay = 300,
      soundEnabled = true
    } = options;

    if (!soundEnabled || !actionLabels?.length) {
      return;
    }

    const currentErrorHash = this.generateErrorHash(actionLabels);
    const now = Date.now();

    // 如果是相同的错误且时间间隔小于指定时间，则忽略
    if (currentErrorHash === this.lastErrorHash && (now - this.lastPlayTime) < duplicateInterval) {
      this.log(`检测到重复的错误推送（间隔${now - this.lastPlayTime}ms < ${duplicateInterval}ms），忽略播放`, 'warn');
      return;
    }

    // 清除之前的防抖定时器
    this.clearDebounceTimer();

    // 如果当前正在播放，停止
    if (this.isPlaying) {
      this.log('检测到新的错误推送，停止当前播放', 'warn');
      this.stopCurrentPlayback();
    }

    this.log(`收到错误推送，将在${debounceDelay}ms后播放`, 'info');

    // 使用防抖机制，延迟播放
    this.debounceTimer = setTimeout(() => {
      // 再次检查是否有更新的错误推送
      if (currentErrorHash !== this.lastErrorHash) {
        this.log('检测到更新的错误推送，取消当前播放', 'warn');
        return;
      }

      this.lastPlayTime = now;
      this.lastErrorHash = currentErrorHash;

      this.log(`开始播放 ${actionLabels.length} 个错误的语音提示`, 'success');
      this.startPlayback(actionLabels);
    }, debounceDelay);
  }

  async startPlayback(actionLabels) {
    this.currentQueue = actionLabels.map(label => `错误${label.actionType}`);
    this.currentIndex = 0;
    this.isPlaying = true;
    this.playNextInQueue();
  }

  async playNextInQueue() {
    if (this.currentIndex >= this.currentQueue.length) {
      // 队列播放完毕
      this.isPlaying = false;
      this.currentQueue = [];
      this.currentIndex = 0;
      this.log('所有错误播放完成', 'success');
      return;
    }

    const errorName = this.currentQueue[this.currentIndex];
    this.log(`正在播放: ${errorName} (${this.currentIndex + 1}/${this.currentQueue.length})`, 'info');

    // 模拟音频播放时间（每个错误2秒）
    setTimeout(() => {
      if (this.isPlaying && this.currentIndex < this.currentQueue.length) {
        this.currentIndex++;
        this.playNextInQueue();
      }
    }, 2000);
  }

  getLogs() {
    return this.logs;
  }

  clearLogs() {
    this.logs = [];
  }
}

// 测试智能错误播放
export const testSmartErrorSound = async () => {
  const tester = new SmartErrorSoundTester();

  console.log('=== 开始测试智能错误播放（带防抖逻辑）===');

  const sameErrors = [
    { actionType: 1 }, // 未双手作业
    { actionType: 2 }, // 未垂直作业面
    { actionType: 8 }, // 未戴手套
  ];

  tester.log('模拟快速连续推送相同错误（间隔100ms）', 'info');

  // 第一次推送
  tester.log('第1次推送', 'info');
  tester.playErrorSoundSmart(sameErrors);

  // 第二次推送（100ms后）
  setTimeout(() => {
    tester.log('第2次推送', 'info');
    tester.playErrorSoundSmart(sameErrors);
  }, 100);

  // 第三次推送（200ms后）
  setTimeout(() => {
    tester.log('第3次推送', 'info');
    tester.playErrorSoundSmart(sameErrors);
  }, 200);

  // 第四次推送（300ms后）
  setTimeout(() => {
    tester.log('第4次推送', 'info');
    tester.playErrorSoundSmart(sameErrors);
  }, 300);

  // 第五次推送（800ms后，应该会播放）
  setTimeout(() => {
    tester.log('第5次推送（延迟较长，应该会播放）', 'info');
    tester.playErrorSoundSmart(sameErrors);
  }, 800);

  console.log('预期结果：');
  console.log('- 前4次推送会被防抖机制处理，只播放最后一次（第4次）');
  console.log('- 第5次推送因为延迟较长，会正常播放');
  console.log('- 总共应该播放2次错误序列');

  // 将测试器挂载到全局，方便查看日志
  if (typeof window !== 'undefined') {
    (window as any).smartTester = tester;
    console.log('测试器已挂载到 window.smartTester，可以调用 window.smartTester.getLogs() 查看详细日志');
  }

  return tester;
};

// 测试不同防抖场景
export const testDebounceScenarios = async () => {
  const tester = new SmartErrorSoundTester();

  console.log('=== 测试不同防抖场景 ===');

  const errors1 = [{ actionType: 1 }, { actionType: 2 }];
  const errors2 = [{ actionType: 3 }, { actionType: 4 }];
  const errors3 = [{ actionType: 1 }, { actionType: 2 }]; // 与errors1相同

  // 场景1：快速连续推送不同错误
  tester.log('=== 场景1：快速连续推送不同错误 ===', 'info');
  tester.playErrorSoundSmart(errors1);

  setTimeout(() => {
    tester.playErrorSoundSmart(errors2); // 不同错误，应该取消前一个
  }, 150);

  // 场景2：相同错误在短时间内重复推送
  setTimeout(() => {
    tester.log('=== 场景2：相同错误重复推送 ===', 'info');
    tester.playErrorSoundSmart(errors1);

    setTimeout(() => {
      tester.playErrorSoundSmart(errors3); // 相同错误，应该被忽略
    }, 500);
  }, 3000);

  // 场景3：正常间隔的错误推送
  setTimeout(() => {
    tester.log('=== 场景3：正常间隔的错误推送 ===', 'info');
    tester.playErrorSoundSmart(errors1);

    setTimeout(() => {
      tester.playErrorSoundSmart(errors2); // 间隔足够长，应该正常播放
    }, 3000);
  }, 8000);

  console.log('预期结果：');
  console.log('- 场景1：只播放errors2（不同错误会取消前一个）');
  console.log('- 场景2：只播放errors1（相同错误在短时间内被忽略）');
  console.log('- 场景3：先播放errors1，然后播放errors2（正常间隔）');

  if (typeof window !== 'undefined') {
    (window as any).debounceTester = tester;
    console.log('测试器已挂载到 window.debounceTester');
  }

  return tester;
};

// 测试操作日志错误处理
export const testOperationLogError = async () => {
  const tester = new SmartErrorSoundTester();

  console.log('=== 测试操作日志错误处理 ===');

  // 模拟操作日志数据
  const operationLogs = [
    {
      id: 1,
      result: 0, // 不合格
      actionLabels: [{ actionType: 1 }, { actionType: 2 }],
      operationTime: new Date().toISOString()
    },
    {
      id: 2,
      result: 1, // 合格，不应该播放
      actionLabels: [{ actionType: 3 }],
      operationTime: new Date().toISOString()
    },
    {
      id: 3,
      result: 2, // 不达标
      actionLabels: [{ actionType: 4 }, { actionType: 5 }],
      operationTime: new Date().toISOString()
    },
    {
      id: 4,
      result: 0, // 不合格，相同错误
      actionLabels: [{ actionType: 1 }, { actionType: 2 }],
      operationTime: new Date().toISOString()
    }
  ];

  // 模拟操作日志错误处理函数
  const handleOperationLogError = (operationLog) => {
    if ((operationLog.result === 0 || operationLog.result === 2) && operationLog.actionLabels?.length) {
      tester.log(`处理操作日志 ID:${operationLog.id}, result:${operationLog.result}`, 'info');
      tester.playErrorSoundSmart(operationLog.actionLabels);
    } else {
      tester.log(`跳过操作日志 ID:${operationLog.id}, result:${operationLog.result}（合格或无错误）`, 'warn');
    }
  };

  // 模拟快速连续的操作日志推送
  operationLogs.forEach((log, index) => {
    setTimeout(() => {
      handleOperationLogError(log);
    }, index * 100); // 每100ms推送一条
  });

  console.log('预期结果：');
  console.log('- 日志1（不合格）：应该播放');
  console.log('- 日志2（合格）：跳过');
  console.log('- 日志3（不达标）：应该播放');
  console.log('- 日志4（不合格，相同错误）：被去重忽略');

  if (typeof window !== 'undefined') {
    (window as any).operationTester = tester;
    console.log('测试器已挂载到 window.operationTester');
  }

  return tester;
};

// 在浏览器控制台中可以直接调用的测试函数
if (typeof window !== 'undefined') {
  (window as any).testErrorSound = {
    // 原有测试
    testOverlap: testErrorSoundOverlap,
    testNormal: testErrorSoundNormal,
    testInterruption: testErrorSoundInterruption,
    testRapidDuplicate: testRapidDuplicateErrors,
    testSingle: testSingleError,
    printInfo: printTestErrorInfo,

    // 新增智能播放测试
    testSmart: testSmartErrorSound,
    testDebounce: testDebounceScenarios,
    testOperationLog: testOperationLogError,
  };

  console.log('错误语音测试函数已挂载到 window.testErrorSound');
  console.log('可用方法:');
  console.log('=== 原有测试 ===');
  console.log('- window.testErrorSound.testOverlap() // 测试快速连续推送');
  console.log('- window.testErrorSound.testNormal() // 测试正常播放');
  console.log('- window.testErrorSound.testInterruption() // 测试播放中被打断');
  console.log('- window.testErrorSound.testRapidDuplicate() // 测试快速连续相同错误');
  console.log('- window.testErrorSound.testSingle() // 测试单个错误');
  console.log('- window.testErrorSound.printInfo() // 打印错误信息');
  console.log('');
  console.log('=== 新增智能播放测试（带防抖逻辑）===');
  console.log('- window.testErrorSound.testSmart() // 测试智能错误播放');
  console.log('- window.testErrorSound.testDebounce() // 测试不同防抖场景');
  console.log('- window.testErrorSound.testOperationLog() // 测试操作日志错误处理');
  console.log('');
  console.log('💡 提示：新测试会在控制台显示彩色日志，并挂载测试器到全局变量');
}
